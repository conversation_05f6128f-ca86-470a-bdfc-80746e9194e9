OBV_GY:
N:=60; M1:=3; M2:=3;
VA:=IF(CLOSE>REF(CLOSE,1),VOL,-VOL);
OBV:=SUM(IF(CLOSE=REF(CLOSE,1),0,VA),0);
RSV:=(OBV-LLV(OBV,N))/(HHV(OBV,N)-LLV(OBV,N))*100;
K: SMA(RSV,M1,1);
D: SMA(K,M2,1);


SKDJ:      (N：  最小：2               最大：90   缺省：7）    (M：  最小：2               最大：30    缺省：3）
LOWV:=LLV(LOW,N);
HIGHV:=HHV(HIGH,N);
RSV:=EMA((CLOSE-LOWV)/(HIGHV-LOWV)*100,M);
K:EMA(RSV,M);
D:MA(K,M);


DMI:   (N：  最小：2               最大：90   缺省：7）    (M：  最小：2               最大：60    缺省：3）
MTR:=SUM(MAX(MAX(HIGH-LOW,ABS(HIGH-REF(CLOSE,1))),ABS(REF(CLOSE,1)-LOW)),N);
HD :=HIGH-REF(HIGH,1);
LD :=REF(LOW,1)-LOW;
DMP:=SUM(IF(HD>0&&HD>LD,HD,0),N);
DMM:=SUM(IF(LD>0&&LD>HD,LD,0),N);
PDI: DMP*100/MTR;
MDI: DMM*100/MTR;
ADX: MA(ABS(MDI-PDI)/(MDI+PDI)*100,M);
ADXR:(ADX+REF(ADX,M))/2;


