基于公式文件中的技术指标，我建议以下策略设计思路：

  基于现有指标的策略建议

  1. 指标组合分析

  现有指标：
  - OBV改进版(OBV_GY)： 基于成交量的动量指标，有K、D两条线
  - SKDJ： 改进版随机指标，反应价格在一定周期内的相对位置
  - DMI： 趋势方向指标，包含PDI(上升方向)、MDI(下降方向)、ADX(趋势强度)

  2. 策略设计建议

  多级信号确认系统：
  - 趋势判断： 用DMI系统判断主趋势方向
    - PDI > MDI：上升趋势
    - MDI > PDI：下降趋势
    - ADX > 25：趋势足够强劲
  - 动量确认： 用OBV_GY确认资金流向
    - K线上穿D线：资金流入加速
    - K线下穿D线：资金流出加速
  - 入场时机： 用SKDJ寻找最佳入场点
    - 超卖区(K<20)反弹：逢低买入
    - 超买区(K>80)调整：减仓或止盈

  3. 具体实施建议

  信号优先级：
  1. 强趋势追踪： ADX > 30 + PDI > MDI + OBV_GY金叉
  2. 逆转抄底： MDI > PDI但ADX下降 + SKDJ超卖反弹
  3. 震荡操作： ADX < 20时，纯粹基于SKDJ超买超卖

  风险控制：
  - 当ADX快速上升但与价格方向相反时，警惕假突破
  - OBV_GY与价格出现背离时，准备趋势反转
  - 三个指标同时给出相反信号时，暂停交易

  4. 参数优化建议

  现有参数可能需要调整：
  - OBV_GY的N=60可能过长，建议测试20-40
  - SKDJ的N=7、M=3适合短线，长线可调至14、5
  - DMI的参数相对合理，可保持

  新增参数：
  - 添加成交量放大倍数阈值
  - 设置止损止盈比例
  - 增加持仓时间限制

  5. 策略优势

  - 多维度验证： 价格、成交量、趋势三重确认
  - 适应性强： 既能抓趋势又能做震荡
  - 风险可控： 有明确的信号冲突处理机制

  建议先用历史数据回测这套指标组合的有效性，再考虑加入你之前提到的3-9秒超短线元素。