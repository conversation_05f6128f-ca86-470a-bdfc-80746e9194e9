第二代通用动态出场模块构建小短波策略(建议转收赞)
图片

量化策略开发，高质量社群，交易思路分享等相关内容



『正文』

ˇ

策略编号：专享策略01_V3

大家好，这是专享01策略的最后一次迭代V3版本，今年剩下的时间我们重点放在专享03策略的开发上。



言归正传，我们在编写策略时经常会遇到一个问题。就是出场参数该如何调整，松鼠之前发布过很多关于出场模块，都各有特点。详情可以翻阅策略帖子。

SF系列策略库>>

算法系列策略库>>

通常，出场模块是有1-2个参数控制，TR是控制整体的出场幅度，X是加速系数用于收敛出场线。如下图：
图片
蓝色线随着时间逐步向K线收敛以做到快速出场，目的是当行情出现回撤时及时出场，保住更多的利润。X起到了调节TR的作用，但是比较有限。任何一个参数都不能适应所有行情，移动出场承担了止损和止盈的作用。



如果TR设定的宽一点出场会变的迟钝，回吐更多的利润承担更多的亏损。

如果TR设定的窄一点出场会变的敏感，频繁出场难以让利润奔跑，整体绩效会差一些。



这么看的话，宽和窄是一个两难的问题，难以解决。我提供一个思路供大家参考。如下图：

图片

OK，有人可能会问，这算什么新的思路。不就是止损+止盈的模式吗？是，整个CTA策略就没有新玩意儿，所有的交易策略不都是开仓平仓吗？请大佬接的往下看。



以前的万金油出场即是止盈也是止损，第二代出场模块严格区分了止盈和止损模块。同时增加了波动率调节+反向收敛这俩个核心内容，一共4个模块。



问：4个模块构建出场是不是增加了很多参数？

答：No,No,No~也考虑了参数数量问题，我一向的宗旨能少用优化参数就尽量少用，仅仅增加了一个参数用于计算初始止损，这个甚至可以用原TR参数代替，总之参数不是重点，留下参数空间供各位自己迭代和优化。



移动止盈--波动率调节



我们在去年的时候出过一个策略：算法策略 | 波动率调节器提高CTA策略盈利能力 主要描述了波动率的变化，大致分为4个区域如下图：



图片

图片

图片

波动率模块是普适性较好的指标，用在非常多的商品上都呈现了相同的规律。我们计算了20多个品种波动率变化，百分位数，标准差，中位数都大相径庭，这样有一个好处是我们不需要优化这个指标，不同品种直接拿来用就行了，如下图：

图片

OK，以前的波动率模块使用方法这样的：

图片



这样的使用其实就是逐bar收敛的作用，类似于X参数。但是第二代动态出场模块决定使用区域划分的方式来解决自适应问题，属于应对模型的思路。如下图：

图片

图片

附图的红柱子就是出场TR参数的变化，使用波动率模块划分出不同的波动率区域，不同区域对应不同的TR参数，这一过程是自适应过程没有需要优化的参数，关于是哪些区域，回上面看波动率变化图。这个模块与X收敛参数不冲突，你可以先划分区域然后搭配收敛系数调节出场。



止损模块--反向收敛



OK，说到止损，大部分都是想到固定止损。但是固定止损有一个致命的问题，不是过宽就过窄契合到模型里行情适应性较弱。因此我们使用反向收敛的思路来构建一个可移动的止损。



什么是反向收敛？如下图：

图片

大概思路是这样，开仓之后给一个初始止损，当价格朝你开仓相反的方向运动时，止损开始收敛，即为反向收敛。


问：为什么不正向收敛？

答：价格朝有利于你的方向运动时，是止盈模块的管理范围，止损不用操心。



止损模块主要解决的痛点是开仓进去价格就朝反方向走，说明你进的位置不太好，这个时候我们尽量缩小我们的亏损，所以要反向收敛止损。这个模块我们设置最多收敛到保本价格，不会无限收敛。效果如下图：

图片

图片

图片


讲解：

第二代动态出场模块将移动止盈和止损分开，这样我们就不用纠结一个移动出场模块面对过宽的参数出场迟钝，承担过大的利润回吐和亏损。过窄的参数又会频繁止损，趋势性差。第二代动态出场模块可以让止盈处理止盈的问题，止损处理止损的问题，释放各自的优势。更多的内容我会在俱乐部直播里讲解代码和思路细节。
“凯撒的归凯撒，上帝的归上帝。”


绩效报告

图片

图片

图片

图片

rb

图片

ap

图片

eb

图片

eg

图片

i

图片

p

图片

j

图片

sa

图片

pp

图片

lh

图片



本策略仅作学习交流使用，实盘交易盈亏投资者个人负责。



👌

End






