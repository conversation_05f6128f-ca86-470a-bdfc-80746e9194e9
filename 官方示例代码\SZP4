//------------------------------------------------------------------------
// 简称: SZP4_1_Long
// 名称: 实战篇4_1_多
// 类别: 公式应用
// 类型: 用户应用
// 输出: Void
//------------------------------------------------------------------------
Params
	//此处添加参数
	Numeric bars(10);
	Numeric TRS(10);
	Numeric time_trigger(10);
	Numeric lots(10);

Vars
	//此处添加变量
	Series<Bool>  first_entry(False);
	Numeric EffRatioLength(5);	//自适应周期数
	Numeric FastAvgLength(2);	//短周期数
	Numeric SlowAvgLength(10);	//长周期数
	Numeric i;
	Numeric i_;
	Series<Bool> up_con;
	Numeric price_up;
	Numeric price_down;
	Numeric volume_up;
	Numeric volume_down;
	Series<Numeric> total_up_down_price;
	Series<Numeric> total_up_down_volume;
	Numeric NetChg(0);
	Numeric TotChg(0);
	Numeric EffRatio(0);
	Numeric ScaledSFSqr(0);
	Series<Numeric> AMAValue;	
	Numeric SFDiff;
		Series<Numeric> adp_loc;
		Numeric fastest;
	Numeric slowest;
		Numeric fast(2);
	Numeric slow(10);
			Series<Numeric> atr;
		Series<Numeric> cc_;
	Series<Numeric> sc;
	Series<Numeric> long_exit_price;
		Series<Numeric> HighAfterEntry;//开仓后出现的最高价
	Series<Numeric> LowAfterEntry;//开仓后出现的最低价
	Series<Numeric> liQKA;
	Series<Numeric> DliqPoint;
	Series<Numeric> KliqPoint;
	Series<Numeric> barcoutN;
	
Events
	//此处实现事件函数
	
	//初始化事件函数，策略运行期间，首先运行且只有一次
	OnInit()
	{
		//=========数据源相关设置==============
		AddDataFlag(Enum_Data_RolloverBackWard());	//设置后复权

		AddDataFlag(Enum_Data_RolloverRealPrice());	//设置映射真实价格

		AddDataFlag(Enum_Data_AutoSwapPosition());	//设置自动换仓

		//AddDataFlag(Enum_Data_IgnoreSwapSignalCalc());	//设置忽略换仓信号计算
	}


	//Bar更新事件函数，参数indexs表示变化的数据源图层ID数组
	OnBar(ArrayRef<Integer> indexs)
	{
		//7 35- 24 16- 37 32-
		//记录开仓后高低点
    	If(BarsSinceentry == 0)
    	{
    		HighAfterEntry = High;
    		LowAfterEntry = Low;
    	}else
    	{
    		HighAfterEntry = Min(HighAfterEntry,High); // 空头止损，更新最低的最高价
    		LowAfterEntry = Max(LowAfterEntry,Low);    // 多头止损，更新最高的最低价
    	}
    	
		up_con = close > open;
		atr = highest(AvgTrueRange(13), 21);
		if (CurrentBar > bars+1)
		{
			For i_=1 To bars
			{
				if (up_con[i_])
				{
					//Commentary("i_="+Text(vol[i_]));
					volume_up = volume_up + vol[i_];
					//Commentary("volume_up="+Text(volume_up));
				}
				Else
				{
					//Commentary("i_="+Text(vol[i_]));
					volume_down = volume_down + vol[i_];
					//Commentary("volume_down="+Text(volume_down));
				}
			}
		}
		
		total_up_down_volume = volume_up - volume_down;
		//PlotNumeric("total_up_down_volume",total_up_down_volume,0,White);
		PlotNumeric("0",0,0,White);

		NetChg = Abs( total_up_down_volume - total_up_down_volume[EffRatioLength]);
		TotChg = Summation( Abs( total_up_down_volume - total_up_down_volume[1] ), EffRatioLength );		
		EffRatio = IIF(TotChg > 0, NetChg / TotChg, 0);
		SFDiff = 2 / ( FastAvgLength + 1 ) - 2 / ( SlowAvgLength + 1 );		
		ScaledSFSqr = Sqr( 2 / ( SlowAvgLength + 1 ) + EffRatio * SFDiff );		
		AMAValue = AMAValue[1] + ScaledSFSqr * ( total_up_down_volume - AMAValue[1] );
		Commentary("EffRatio"+Text(EffRatio));
		//PlotNumeric("AMAValue",AMAValue,0,red);
		
		
		fastest = 2 / (fast + 1);
        slowest = 2 / (slow + 1);
		if ( AMAValue>0)
		{
			if (MarketPosition==0 and first_entry==False)
			{
				Buy(10, Open);
				first_entry = True;
			}
			if (MarketPosition==0 and first_entry and BarsSinceExit>time_trigger)
			{
				Buy(10, Open);
			}
			PlotNumeric("AMAValue",AMAValue,0,red);
		}
		Else
		{
			PlotNumeric("AMAValue",AMAValue,0,green);
		}
		
		// 出场
		    	//移动出场
    	If(MarketPosition == 0)   // 自适应参数默认值；
    	{
    		liQKA = 1; 
    		barcoutN=0;
    	}Else if(BarsSinceEntry>barcoutN)					 //当有持仓的情况下，liQKA会随着持仓时间的增加而逐渐减小，即止损止盈幅度乘数的减少。
    	{
    		liQKA = liQKA - 0.1; 
    		liQKA = Max(liQKA,0.3);
    		barcoutN=BarsSinceEntry;
    	}
    	if(MarketPosition>0)
    	{
			DliqPoint = LowAfterEntry - (Open*TRS/1000)*liQKA; //经过计算，这根吊灯出场线会随着持仓时间的增加变的越来越敏感；
    	}
    	if(MarketPosition<0)
    	{
			KliqPoint = HighAfterEntry + (Open*TRS/1000)*liQKA; //经过计算，这根吊灯出场线会随着持仓时间的增加变的越来越敏感；
    	}
    	   
    	// 持有多单时
    	If(MarketPosition >0 And BarsSinceEntry >0  And Low <= DliqPoint[1] and DliqPoint[1]>0) 
    	{
    	
    			Sell(0,Min(Open,DliqPoint[1]));
    			DliqPoint=0;
    			barcoutN=0; 
    
    	}
    	
	}

//------------------------------------------------------------------------
// 编译版本	2022/09/13 102556
// 版权所有	hitlerls
// 更改声明	TradeBlazer Software保留对TradeBlazer平台
//			每一版本的TradeBlazer公式修改和重写的权利
//------------------------------------------------------------------------