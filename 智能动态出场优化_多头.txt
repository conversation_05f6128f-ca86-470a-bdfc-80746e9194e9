{第三代智能动态出场模块 - 优化版}
{仅包含多头策略逻辑 - OPTIMIZED LONG-ONLY VERSION}
基础TR:2.0    {基础止盈止损倍数}
初始止损:1.5  {初始止损ATR倍数}
波动率周期:20  {波动率计算周期}
趋势强度周期:10 {趋势强度评估周期}
ADX周期:14     {新增：趋势确认周期}

{1. 增强型多维度波动率评估模块}
{价格波动率}
当前ATR:=ATR(波动率周期);
ATR均值:=MA(当前ATR,波动率周期*2);
价格波动率:=当前ATR/ATR均值;

{成交量波动率}
量能比:=VOL/MA(VOL,波动率周期);
量能波动率:=STD(量能比,波动率周期)/MA(量能比,波动率周期);

{时间波动率}
价格变化率:=ABS(CLOSE/REF(CLOSE,1)-1);
时间波动率:=MA(价格变化率,5)/MA(价格变化率,波动率周期);

{新增：波动率突变因子}
波动率变化率:=ABS(当前ATR/REF(当前ATR,1)-1);
波动率突变因子:=IF(波动率变化率>0.3,1.5,1);

{优化权重的综合波动率评分}
综合波动率:=(价格波动率*0.4+量能波动率*0.35+时间波动率*0.25)*波动率突变因子;

{2. 升级趋势识别模块}
{MA均线系统}
MA5:=MA(CLOSE,5);
MA10:=MA(CLOSE,10);
MA20:=MA(CLOSE,20);
趋势方向一致:=(MA5>MA10 AND MA10>MA20) OR (MA5<MA10 AND MA10<MA20);

{手动实现ADX指标计算}
  // 计算真实波幅(TR)
  计算用TR:=MAX(MAX(HIGH-LOW,ABS(HIGH-REF(CLOSE,1))),ABS(LOW-REF(CLOSE,1)));
  // 计算+DM和-DM
  PLUS_DM:=HIGH>REF(HIGH,1) AND HIGH-REF(HIGH,1)>REF(LOW,1)-LOW ? HIGH-REF(HIGH,1) : 0;
  MINUS_DM:=LOW<REF(LOW,1) AND REF(LOW,1)-LOW>HIGH-REF(HIGH,1) ? REF(LOW,1)-LOW : 0;
  // 平滑处理
  SMOOTH_TR:=SMA(计算用TR,14,1);
  SMOOTH_PLUS_DM:=SMA(PLUS_DM,14,1);
  SMOOTH_MINUS_DM:=SMA(MINUS_DM,14,1);
  // 计算+DI和-DI
  PLUS_DI:=SMOOTH_PLUS_DM/SMOOTH_TR*100;
  MINUS_DI:=SMOOTH_MINUS_DM/SMOOTH_TR*100;
  // 计算DX和ADX
  DX:=ABS(PLUS_DI-MINUS_DI)/(PLUS_DI+MINUS_DI)*100;
  趋势强度指标:=SMA(DX,14,1);
  ADX趋势确认:=趋势强度指标>25;

{价格偏离度与趋势强度}
价格偏离度:=ABS(CLOSE/MA20-1);
趋势强度:=IF(ADX趋势确认,价格偏离度*1.5,价格偏离度)*
          IF(趋势方向一致,1.2,1);

{市场状态判断}
强趋势市:=趋势强度>3 AND 趋势方向一致 AND ADX趋势确认;
震荡市:=趋势强度<1.5 AND ADX<=25;
弱趋势市:=NOT 强趋势市 AND NOT 震荡市;

{3. 智能波动率区域划分}
{动态阈值计算}
历史波动率:=MA(综合波动率,波动率周期*3);
波动率标准差:=STD(综合波动率,波动率周期*2);
下阈值:=历史波动率-波动率标准差*0.5;
上阈值:=历史波动率+波动率标准差*0.5;
极值阈值:=历史波动率+波动率标准差*1.2;

{智能区域划分}
低波动区:=综合波动率<=下阈值;
正常波动区:=综合波动率>下阈值 AND 综合波动率<=上阈值;
高波动区:=综合波动率>上阈值 AND 综合波动率<=极值阈值;
极高波动区:=综合波动率>极值阈值;

{4. 环境自适应TR调整}
{基础TR调整}
TR基础:=IF(低波动区,基础TR*1.8,IF(正常波动区,基础TR,IF(高波动区,基础TR*0.6,基础TR*0.4)));

{趋势环境修正}
TR趋势修正:=IF(强趋势市,TR基础*1.2,IF(震荡市,TR基础*0.8,TR基础));

{最终动态TR}
动态TR:=TR趋势修正;

{5. 增强型移动止盈模块}
{波动率调节的止盈基准}
多头止盈基准:=CLOSE-当前ATR*动态TR;

{新增：趋势加速因子}
趋势加速因子:=IF(MA5>REF(MA5,1) AND MA10>REF(MA10,1),1.1,1);
多头止盈基准:=多头止盈基准*趋势加速因子;

{只能朝有利方向移动}
多头止盈线:=HHV(多头止盈基准,3);

{6. 智能反向收敛止损模块}
{初始止损计算}
多头初始止损:=CLOSE-当前ATR*初始止损;

{波动率自适应收敛速度}
收敛速度:=IF(高波动区,0.6,IF(低波动区,0.3,0.5));

{反向收敛逻辑 - 价格下跌时止损上移}
价格下跌幅度:=MAX(0,REF(CLOSE,1)-CLOSE);
收敛比例:=价格下跌幅度/(当前ATR*初始止损);
多头收敛系数:=MIN(收敛比例*收敛速度,0.8); {最多收敛80%}

{止损基准与动态调整}
多头止损基准:=多头初始止损+当前ATR*初始止损*多头收敛系数;
多头动态止损:=MIN(多头止损基准,CLOSE); {不超过保本价格}

{7. 显示模块}
{移动止盈线显示}
多头止盈线,COLORGREEN,LINETHICK2;

{动态止损线显示}
多头动态止损,COLORBLUE,LINETHICK1,POINTDOT;

{新增：趋势强度可视化}
趋势强度直方图:=趋势强度*10,COLORSTICK;