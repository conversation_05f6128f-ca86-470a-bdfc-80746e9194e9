// =================================================================
// 多指标组合策略 - 基于OBV_GY、SKDJ、DMI (单边做多版本)
// Multi-Indicator Long-Only Strategy for TBquant Platform
// 创建日期: 2025-07-23
// 说明: 结合成交量、动量和趋势指标的综合交易策略 - 仅做多头交易
// =================================================================

// 策略参数定义
Params
    // OBV_GY参数
    Numeric OBV_N(60);           // OBV计算周期
    Numeric OBV_M1(3);           // OBV的K线平滑参数
    Numeric OBV_M2(3);           // OBV的D线平滑参数
    
    // SKDJ参数  
    Numeric SKDJ_N(7);           // SKDJ周期
    Numeric SKDJ_M(3);           // SKDJ平滑参数
    
    // DMI参数
    Numeric DMI_N(7);            // DMI计算周期
    Numeric DMI_M(3);            // ADX平滑参数
    
    // 交易参数
    Numeric BuyThreshold(20);     // 超卖阈值
    Numeric SellThreshold(80);    // 超买阈值
    Numeric ADXThreshold(25);     // 趋势强度阈值
    Numeric StopLoss(0.05);       // 止损比例(5%)
    Numeric TakeProfit(0.10);     // 止盈比例(10%)

    // 资金管理参数 (全仓模式)
    Numeric ReserveRatio(0.02);   // 资金保留比例(2% 用于手续费等)
    Numeric MinCash(100);         // 最小保留现金
    Numeric InitialCapital(100000); // 初始资金
    Numeric LotSize(10);          // 最小交易单位(10股每手)

// 变量定义      
Vars
    // OBV_GY相关变量
    Numeric VolumeDirection(0);      // 成交量方向
    Series<Numeric> OBVValue(0);     // 修正OBV值
    Series<Numeric> OBV_RSV(0);      // OBV的RSV
    Series<Numeric> OBV_K(0);        // OBV的K值
    Series<Numeric> OBV_D(0);        // OBV的D值
    
    // SKDJ相关变量
    Series<Numeric> LOWV(0);         // N周期最低价
    Series<Numeric> HIGHV(0);        // N周期最高价
    Series<Numeric> SKDJ_RSV(0);     // SKDJ的RSV
    Series<Numeric> SKDJ_K(0);       // SKDJ的K值
    Series<Numeric> SKDJ_D(0);       // SKDJ的D值
    
    // DMI相关变量
    Series<Numeric> MTR(0);          // 真实波动
    Series<Numeric> HD(0);           // 上升动向
    Series<Numeric> LD(0);           // 下降动向
    Series<Numeric> DMP(0);          // 上升动向值
    Series<Numeric> DMM(0);          // 下降动向值
    Series<Numeric> PDI(0);          // 正向指标
    Series<Numeric> MDI(0);          // 负向指标
    Series<Numeric> ADX(0);          // 平均趋向指标
    Series<Numeric> ADXR(0);         // 趋向平均值
    
    // 交易控制变量
    Series<Bool> BuySignal(False);   // 买入信号
    Series<Bool> SellSignal(False);  // 平仓信号
    Series<Bool> TrendUp(False);     // 上升趋势
    Series<Bool> TrendDown(False);   // 下降趋势
    Series<Bool> StrongTrend(False); // 强趋势
    Series<Numeric> EntryPrice(0);   // 入场价格

    // 资金管理变量
    Numeric AvailableCash(0);        // 可用现金
    Numeric BuyQuantity(0);          // 实际买入数量

    // 交易控制变量
    Series<Bool> TradeExecuted(False); // 本Bar是否已执行交易
    Series<Bool> StopLossHit(False);   // 是否触发止损
    Series<Bool> TakeProfitHit(False); // 是否触发止盈

    // 临时变量 (避免序列函数在控制结构中使用)
    Numeric PrevClose(0);            // 前一日收盘价
    Numeric PrevEntryPrice(0);       // 前一日入场价格
    Numeric PrevHigh(0);             // 前一日最高价
    Numeric PrevLow(0);              // 前一日最低价
    Numeric PrevSKDJ_K(0);           // 前一日SKDJ_K值
    Numeric PrevADX(0);              // 前一期ADX值

Events
    OnBar(ArrayRef<Integer> indexs)
    {
        // 初始化交易控制变量
        TradeExecuted = False;
        StopLossHit = False;
        TakeProfitHit = False;

        // 获取序列变量值 (避免在控制结构中直接使用序列函数)
        PrevClose = C[1];
        PrevEntryPrice = EntryPrice[1];
        PrevHigh = H[1];
        PrevLow = L[1];
        PrevSKDJ_K = SKDJ_K[1];
        PrevADX = ADX[DMI_M];

        // =============================================================
        // 1. 计算OBV_GY指标
        // =============================================================

        // 计算成交量方向
        If(C > PrevClose)
        {
            VolumeDirection = Vol;
        }
        ElseIf(C < PrevClose)
        {
            VolumeDirection = -Vol;
        }
        Else
        {
            VolumeDirection = 0;
        };
        
        // 累计OBV
        OBVValue = OBVValue[1] + VolumeDirection;
        
        // 计算OBV的RSV
        If((Highest(OBVValue, OBV_N) - Lowest(OBVValue, OBV_N)) != 0)
        {
            OBV_RSV = (OBVValue - Lowest(OBVValue, OBV_N)) / (Highest(OBVValue, OBV_N) - Lowest(OBVValue, OBV_N)) * 100;
        }
        Else
        {
            OBV_RSV = 50;
        };
        
        // 计算OBV的K值和D值
        OBV_K = Average(OBV_RSV, OBV_M1);
        OBV_D = Average(OBV_K, OBV_M2);
        
        // =============================================================
        // 2. 计算SKDJ指标
        // =============================================================
        
        // 计算N周期内的最高价和最低价
        LOWV = Lowest(L, SKDJ_N);
        HIGHV = Highest(H, SKDJ_N);
        
        // 计算SKDJ的RSV
        If((HIGHV - LOWV) != 0)
        {
            SKDJ_RSV = (C - LOWV) / (HIGHV - LOWV) * 100;
        }
        Else
        {
            SKDJ_RSV = 50;
        };
        
        // 计算SKDJ的K值和D值
        SKDJ_K = Average(SKDJ_RSV, SKDJ_M);
        SKDJ_D = Average(SKDJ_K, SKDJ_M);
        
        // =============================================================
        // 3. 计算DMI指标
        // =============================================================
        
        // 计算真实波动率
        MTR = Summation(Max(Max(H - L, Abs(H - PrevClose)), Abs(PrevClose - L)), DMI_N);

        // 计算方向动量
        HD = H - PrevHigh;
        LD = PrevLow - L;
        
        // 计算上升和下降动向值
        Numeric DMPValue(0);
        Numeric DMMValue(0);

        If(HD > 0 And HD > LD)
        {
            DMPValue = HD;
        }
        Else
        {
            DMPValue = 0;
        };

        If(LD > 0 And LD > HD)
        {
            DMMValue = LD;
        }
        Else
        {
            DMMValue = 0;
        };

        DMP = Summation(DMPValue, DMI_N);
        DMM = Summation(DMMValue, DMI_N);
        
        // 计算PDI和MDI
        If(MTR != 0)
        {
            PDI = DMP * 100 / MTR;
            MDI = DMM * 100 / MTR;
        }
        Else
        {
            PDI = 0;
            MDI = 0;
        };
        
        // 计算ADX和ADXR
        If((MDI + PDI) != 0)
        {
            ADX = Average(Abs(MDI - PDI) / (MDI + PDI) * 100, DMI_M);
        }
        Else
        {
            ADX = 0;
        };
        
        ADXR = (ADX + PrevADX) / 2;
        
        // =============================================================
        // 4. 趋势和信号判断
        // =============================================================
        
        // 趋势判断
        TrendUp = PDI > MDI;
        TrendDown = MDI > PDI;
        StrongTrend = ADX > ADXThreshold;
        
        // 买入信号组合判断 (优化版本 - 更严格的条件)
        Series<Bool> BuyCondition1(False);
        Series<Bool> BuyCondition2(False);

        // 条件1: 强趋势买入 (所有条件必须同时满足)
        BuyCondition1 = (TrendUp And StrongTrend And
                        CrossOver(OBV_K, OBV_D) And
                        SKDJ_K < BuyThreshold And SKDJ_K > PrevSKDJ_K And
                        OBV_K > 50 And SKDJ_K > 10);  // 增加过滤条件

        // 条件2: 震荡市买入 (所有条件必须同时满足)
        BuyCondition2 = (Not TrendDown And Not StrongTrend And
                        OBV_K > OBV_D And
                        SKDJ_K < BuyThreshold And CrossOver(SKDJ_K, SKDJ_D) And
                        ADX < 30 And SKDJ_K > 5);  // 增加过滤条件

        BuySignal = (BuyCondition1 Or BuyCondition2);

        // 平仓信号组合判断 (优化版本 - 更明确的退出条件)
        Series<Bool> SellCondition1(False);
        Series<Bool> SellCondition2(False);
        Series<Bool> SellCondition3(False);

        // 条件1: 强下降趋势
        SellCondition1 = (TrendDown And StrongTrend And
                         CrossUnder(OBV_K, OBV_D) And
                         ADX > 30);  // 确保是真正的强趋势

        // 条件2: 超买死叉
        SellCondition2 = (SKDJ_K > SellThreshold And CrossUnder(SKDJ_K, SKDJ_D) And
                         SKDJ_K < 95);  // 避免极端超买时的假信号

        // 条件3: 双重警告
        SellCondition3 = (OBV_K < OBV_D And SKDJ_K > SellThreshold And
                         OBV_K < 40);  // 确保资金明显流出

        SellSignal = (SellCondition1 Or SellCondition2 Or SellCondition3);
        
        // =============================================================
        // 5. 交易执行逻辑 (单边做多) - 优化版本
        // =============================================================

        // 优先级1: 止损止盈检查 (最高优先级)
        If(MarketPosition > 0 And Not TradeExecuted)
        {
            Numeric StopLossPrice(0);
            Numeric TakeProfitPrice(0);
            StopLossPrice = PrevEntryPrice * (1 - StopLoss);
            TakeProfitPrice = PrevEntryPrice * (1 + TakeProfit);

            // 止损: 价格跌破入场价的(1-止损比例)
            If(C <= StopLossPrice)
            {
                Sell(MarketPosition, Open);
                StopLossHit = True;
                TradeExecuted = True;
                // 输出止损信息
                Commentary("止损触发");
            }
            // 止盈: 价格涨超入场价的(1+止盈比例)
            ElseIf(C >= TakeProfitPrice)
            {
                Sell(MarketPosition, Open);
                TakeProfitHit = True;
                TradeExecuted = True;
                // 输出止盈信息
                Commentary("止盈触发");
            };
        };

        // 优先级2: 信号平仓 (中等优先级)
        If(SellSignal And MarketPosition > 0 And Not TradeExecuted)
        {
            Sell(MarketPosition, Open);
            TradeExecuted = True;
            // 输出信号平仓信息
            Commentary("信号平仓");
        };

        // 优先级3: 买入逻辑 (最低优先级)
        If(BuySignal And MarketPosition <= 0 And Not TradeExecuted)
        {
            // 使用简化的资金计算方法
            AvailableCash = InitialCapital - MinCash;

            // 资金安全检查
            If(AvailableCash < InitialCapital * 0.1)
            {
                AvailableCash = InitialCapital * 0.1;  // 最少保留10%资金
            };

            // 扣除保留比例
            AvailableCash = AvailableCash * (1 - ReserveRatio);

            // 确保有足够资金
            If(AvailableCash > MinCash And AvailableCash > Open * LotSize)
            {
                // 计算全仓买入数量 (可用资金/开盘价)
                Numeric TotalShares(0);
                TotalShares = AvailableCash / Open;

                // 按10股每手调整为整手数
                Numeric Lots(0);
                Lots = TotalShares / LotSize;
                BuyQuantity = Lots * LotSize;

                // 执行全仓买入 (确保是10股的倍数)
                If(BuyQuantity >= LotSize)
                {
                    Buy(BuyQuantity, Open);
                    EntryPrice = C;
                    TradeExecuted = True;
                    // 输出买入信息
                    Commentary("买入执行");
                }
                Else
                {
                    Commentary("资金不足");
                };
            }
            Else
            {
                Commentary("资金检查失败");
            };
        };
    }

//------------------------------------------------------------------------
// 编译版本	GS2014.10.25
// 版权所有	TradeBlazer Software 2003－2025
// 更改声明	TradeBlazer Software保留对TradeBlazer平
//			台每一版本的TradeBlazer公式修改和重写的权利
//------------------------------------------------------------------------


// =================================================================
// 策略说明 (单边做多优化版本):
// 1. 结合OBV成交量指标、SKDJ动量指标和DMI趋势指标
// 2. 多重信号确认，减少假信号
// 3. 仅做多头交易，适用于长期看涨的市场
// 4. 内置止损止盈机制
// 5. 在下降趋势中避免开仓，降低风险
//
// 优化特性:
// - 交易优先级控制: 止损止盈 > 信号平仓 > 买入开仓
// - 信号冲突处理: 防止同一Bar内重复交易
// - 增强过滤条件: 减少假信号，提高信号质量
// - 资金安全检查: 防止过度亏损，保留最低资金
// - 详细日志输出: 便于策略调试和分析
//
// 交易逻辑 (优化版):
// - 买入条件1: 强趋势+资金流入+超卖反弹+过滤条件
// - 买入条件2: 震荡市+资金积极+超卖金叉+过滤条件
// - 平仓条件1: 强下降趋势+资金流出+趋势确认
// - 平仓条件2: 超买死叉+避免极端情况
// - 平仓条件3: 双重警告+资金明显流出
// - 风控: 5%止损，10%止盈 (最高优先级)
//
// 资金管理 (全仓模式优化):
// - 买入: 使用全部可用资金买入 (扣除保留资金)
// - 卖出: 清空所有持仓
// - 资金保护: 最少保留10%初始资金
// - ReserveRatio: 保留资金比例 (用于手续费等)
// - MinCash: 最小保留现金
// - LotSize: 最小交易单位 (10股每手)
//
// 使用建议:
// - 适合日线及以上周期
// - 适合长期看涨的品种
// - 建议先进行充分回测
// - 可根据不同品种调整参数
// - 关注Commentary输出进行策略调试
// =================================================================



