ADX：
参数（n：23，m：11）
MTR:=SUM(MAX(MAX(HIGH-LOW,ABS(HIGH-REF(CLOSE,1))),ABS(REF(CLOSE,1)-LOW)),N);
HD :=HIGH-REF(HIGH,1);
LD :=REF(LOW,1)-LOW;
DMP:=SUM(IF(HD>0&&HD>LD,HD,0),N);
DMM:=SUM(IF(LD>0&&LD>HD,LD,0),N);
PDI: DMP*100/MTR;
MDI: DMM*100/MTR;
ADX: MA(ABS(MDI-PDI)/(MDI+PDI)*100,M);
ADXR:(ADX+REF(ADX,M))/2;
===============================================================
SKDJ：
参数（n：8，m：4）
LOWV:=LLV(LOW,N);
HIGHV:=HHV(HIGH,N);
RSV:=EMA((CLOSE-LOWV)/(HIGHV-LOWV)*100,M);
K:EMA(RSV,M);
D:MA(K,M);
===============================================================
CMF：

{CHAIKIN MONEY FLOW 背离指标 - CMF DIVERGENCE}
参数（n：30，m：20）


{计算收盘位置值 (CLV)}
CLV:=(CLOSE-LOW-HIGH+CLOSE)/(HIGH-LOW);

{计算资金流量}
MF:=CLV*VOL;

{计算CMF}
CMF:=SUM(MF,N)/SUM(VOL,N);

{价格高低点判断}
HH:=HIGH>=HHV(HIGH,M);  {价格创新高}
LL:=LOW<=LLV(LOW,M);    {价格创新低}

{CMF高低点判断}
CMFHH:=CMF>=HHV(CMF,M);  {CMF创新高}
CMFLL:=CMF<=LLV(CMF,M);  {CMF创新低}

{顶背离：价格创新高但CMF未创新高}
顶背离:=HH AND CMFHH=0 AND CMF>0;

{底背离：价格创新低但CMF未创新低}
底背离:=LL AND CMFLL=0 AND CMF<0;

{显示CMF线}
CMF,COLORWHITE,LINETHICK1;
0,LINETHICK1,COLORBLUE;

{背离信号显示}
STICKLINE(顶背离,0,CMF,3,0),COLORRED;
STICKLINE(底背离,0,CMF,3,0),COLORGREEN;

{背离点标记}
{DRAWICON(顶背离,CMF,1);  {向下箭头表示顶背离}
{DRAWICON(底背离,CMF,2);  {向上箭头表示底背离}

{背离信号文字提示}
{DRAWTEXT(顶背离,CMF,'顶背离'),COLORRED;
DRAWTEXT(底背离,CMF,'底背离'),COLORGREEN;};

===============================================================
BIAS：

{乖离率背离指标 - BIAS DIVERGENCE}
参数（n：30，m：20）

{计算乖离率}
BIAS:=(CLOSE-MA(CLOSE,N))/MA(CLOSE,N)*100;

{价格高低点判断}
PH:=HIGH>=HHV(HIGH,M);  {价格创新高}
PL:=LOW<=LLV(LOW,M);    {价格创新低}

{乖离率高低点判断}
BIASH:=BIAS>=HHV(BIAS,M);  {乖离率创新高}
BIASL:=BIAS<=LLV(BIAS,M);  {乖离率创新低}

{背离判断}
顶背离:=PH AND BIASH=0 AND BIAS>0;
底背离:=PL AND BIASL=0 AND BIAS<0;

{显示乖离率线}
BIAS,COLORWHITE,LINETHICK1;
0,LINETHICK1,COLORBLUE;

{背离信号显示}
STICKLINE(顶背离,0,BIAS,3,0),COLORRED;
STICKLINE(底背离,0,BIAS,3,0),COLORGREEN;

{背离点标记}
DRAWICON(顶背离,BIAS,1);  {顶背离向下箭头}
DRAWICON(底背离,BIAS,2);  {底背离向上箭头}

{背离信号文字提示}
DRAWTEXT(顶背离,BIAS,'顶背离'),COLORRED;
DRAWTEXT(底背离,BIAS,'底背离'),COLORGREEN;

===============================================================
VAE：
参数（基础TR：1.8，初始止损：1.5，波动率周期：20）

{1. 波动率模块 - 区域划分}
当前ATR:=ATR(波动率周期);
ATR均值:=MA(当前ATR,波动率周期*2);
波动率比值:=当前ATR/ATR均值;

{波动率区域划分}
低波动区:=波动率比值<=0.8;
正常波动区:=波动率比值>0.8 AND 波动率比值<=1.2;
高波动区:=波动率比值>1.2 AND 波动率比值<=1.8;
极高波动区:=波动率比值>1.8;

{动态TR参数调整}
TR1:=IF(低波动区,基础TR*2,基础TR);
TR2:=IF(高波动区,基础TR*1,TR1);
动态TR:=IF(极高波动区,基础TR*0.7,TR2);

{2. 移动止盈模块 - 波动率调节}
多头止盈基准:=CLOSE-当前ATR*动态TR;

{只能朝有利方向移动}
多头止盈线:=HHV(多头止盈基准,3);

{3. 反向收敛止损模块}
{假设多头仓位计算}
多头初始止损:=CLOSE-当前ATR*初始止损;
多头当前价格:=CLOSE;

{反向收敛逻辑 - 价格下跌时止损上移}
价格下跌幅度:=MAX(0,REF(CLOSE,1)-CLOSE);
收敛比例:=价格下跌幅度/(当前ATR*初始止损);
多头收敛系数:=MIN(收敛比例,0.8); {最多收敛80%}

多头止损基准:=多头初始止损+当前ATR*初始止损*多头收敛系数;
多头动态止损:=MIN(多头止损基准,CLOSE); {不超过保本价格}


===============================================================
阻力线计算：
K线的加权均值 = (最高价+最低价+2*收盘价)/4
阻力线 = K线加权均值 + ( K线加权均值 - 最低价)
入场条件:
当价格向上突破阻力线做多






策略：
买入条件：
SKDJ的K和D都小于20
CMF底背离或者前两根k线底背离
BIAS底背离或者前两根k线底背离
ADX > 40
当价格向上突破阻力线

当上面条件都满足时买入

卖出：
亏损 > 0.5%
VAE模块

当上面任何一个条件满足时卖出

===============================================================
{策略总结与实现方案}

{一、可复用的核心公式模块}

{1. 背离检测核心算法（通用模板）}
{价格高低点判断}
价格新高:=HIGH>=HHV(HIGH,M);
价格新低:=LOW<=LLV(LOW,M);

{指标高低点判断}
指标新高:=指标值>=HHV(指标值,M);
指标新低:=指标值<=LLV(指标值,M);

{背离判断逻辑}
顶背离:=价格新高 AND 指标新高=0 AND 指标值>0;
底背离:=价格新低 AND 指标新低=0 AND 指标值<0;

{2. 波动率自适应模块（VAE）- 可直接复用}
{已实现动态止盈止损，可作为风控核心}

{3. 趋势强度过滤（ADX）- 可直接复用}
{ADX>40确保在强趋势中交易}

{二、策略完整实现框架}

{参数设置}
SKDJ_N:8;    SKDJ_M:4;
CMF_N:30;    CMF_M:20;
BIAS_N:30;   BIAS_M:20;
ADX_N:23;    ADX_M:11;
VAE_基础TR:1.8; VAE_初始止损:1.5; VAE_波动率周期:20;
止损百分比:0.5;

{核心指标计算 - 复用现有公式}
{SKDJ计算}
LOWV:=LLV(LOW,SKDJ_N);
HIGHV:=HHV(HIGH,SKDJ_N);
RSV:=EMA((CLOSE-LOWV)/(HIGHV-LOWV)*100,SKDJ_M);
K:=EMA(RSV,SKDJ_M);
D:=MA(K,SKDJ_M);

{CMF背离计算}
CLV:=(CLOSE-LOW-HIGH+CLOSE)/(HIGH-LOW);
MF:=CLV*VOL;
CMF:=SUM(MF,CMF_N)/SUM(VOL,CMF_N);
CMF_HH:=HIGH>=HHV(HIGH,CMF_M);
CMF_LL:=LOW<=LLV(LOW,CMF_M);
CMF_指标HH:=CMF>=HHV(CMF,CMF_M);
CMF_指标LL:=CMF<=LLV(CMF,CMF_M);
CMF顶背离:=CMF_HH AND CMF_指标HH=0 AND CMF>0;
CMF底背离:=CMF_LL AND CMF_指标LL=0 AND CMF<0;

{BIAS背离计算}
BIAS:=(CLOSE-MA(CLOSE,BIAS_N))/MA(CLOSE,BIAS_N)*100;
BIAS_HH:=HIGH>=HHV(HIGH,BIAS_M);
BIAS_LL:=LOW<=LLV(LOW,BIAS_M);
BIAS_指标HH:=BIAS>=HHV(BIAS,BIAS_M);
BIAS_指标LL:=BIAS<=LLV(BIAS,BIAS_M);
BIAS顶背离:=BIAS_HH AND BIAS_指标HH=0 AND BIAS>0;
BIAS底背离:=BIAS_LL AND BIAS_指标LL=0 AND BIAS<0;

{ADX趋势强度}
MTR:=SUM(MAX(MAX(HIGH-LOW,ABS(HIGH-REF(CLOSE,1))),ABS(REF(CLOSE,1)-LOW)),ADX_N);
HD:=HIGH-REF(HIGH,1);
LD:=REF(LOW,1)-LOW;
DMP:=SUM(IF(HD>0&&HD>LD,HD,0),ADX_N);
DMM:=SUM(IF(LD>0&&LD>HD,LD,0),ADX_N);
PDI:=DMP*100/MTR;
MDI:=DMM*100/MTR;
ADX:=MA(ABS(MDI-PDI)/(MDI+PDI)*100,ADX_M);

{阻力线计算}
K线加权均值:=(HIGH+LOW+2*CLOSE)/4;
阻力线:=K线加权均值+(K线加权均值-LOW);

{买入条件组合}
SKDJ条件:=K<20 AND D<20;
CMF背离条件:=CMF底背离 OR REF(CMF底背离,1) OR REF(CMF底背离,2);
BIAS背离条件:=BIAS底背离 OR REF(BIAS底背离,1) OR REF(BIAS底背离,2);
ADX条件:=ADX>40;
突破条件:=CLOSE>阻力线 AND REF(CLOSE,1)<=REF(阻力线,1);

{最终买入信号}
买入信号:=SKDJ条件 AND CMF背离条件 AND BIAS背离条件 AND ADX条件 AND 突破条件;

{卖出条件}
{1. 固定止损}
亏损止损:=(CLOSE-COST)/COST<-止损百分比/100;

{2. VAE动态止损止盈 - 复用现有模块}
当前ATR:=ATR(VAE_波动率周期);
ATR均值:=MA(当前ATR,VAE_波动率周期*2);
波动率比值:=当前ATR/ATR均值;
低波动区:=波动率比值<=0.8;
正常波动区:=波动率比值>0.8 AND 波动率比值<=1.2;
高波动区:=波动率比值>1.2 AND 波动率比值<=1.8;
极高波动区:=波动率比值>1.8;
TR1:=IF(低波动区,VAE_基础TR*2,VAE_基础TR);
TR2:=IF(高波动区,VAE_基础TR*1,TR1);
动态TR:=IF(极高波动区,VAE_基础TR*0.7,TR2);
多头止盈基准:=CLOSE-当前ATR*动态TR;
多头止盈线:=HHV(多头止盈基准,3);
VAE卖出:=CLOSE<=多头止盈线;

{最终卖出信号}
卖出信号:=亏损止损 OR VAE卖出;

{三、接下来的实现步骤}

{第一阶段：基础框架搭建（1-2天）}
1. 整合所有指标计算公式到一个主策略文件
2. 实现买入卖出信号的逻辑判断
3. 添加信号显示和标记功能
4. 进行基础的语法检查和调试

{第二阶段：参数优化（3-5天）}
1. 对各个指标的参数进行历史回测
2. 优化SKDJ、CMF、BIAS的周期参数
3. 调整ADX阈值和VAE的TR参数
4. 测试不同市场环境下的表现

{第三阶段：风控完善（2-3天）}
1. 完善VAE模块的动态调整机制
2. 添加仓位管理功能
3. 实现多重确认机制避免假信号
4. 加入市场环境识别功能

{第四阶段：实盘验证（持续）}
1. 小仓位实盘测试
2. 监控策略表现并记录
3. 根据实盘反馈调整参数
4. 逐步增加仓位规模

{关键技术要点}
- 背离检测的时间窗口要合理，避免过于敏感
- ADX>40的强趋势过滤很重要，可以避免震荡市的假突破
- VAE模块的动态调整是核心优势，要充分利用
- 多指标确认可以提高信号质量，但也会减少交易频率
- 需要考虑不同品种的特性差异，可能需要分别调参

{预期效果}
- 胜率：预期60-70%（多重确认机制）
- 盈亏比：1:2以上（VAE动态止盈）
- 交易频率：中等（严格的多重过滤）
- 最大回撤：控制在10%以内（动态风控）