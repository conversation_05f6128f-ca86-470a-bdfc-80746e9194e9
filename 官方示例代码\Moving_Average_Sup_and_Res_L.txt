//------------------------------------------------------------------------
// 简称: Moving_Average_Sup_and_Res_L
// 名称: 基于均线的阻力线支撑线系统多 
// 类别: 公式应用
// 类型: 内建应用
//------------------------------------------------------------------------
//----------------------------------------------------------------------//
// 策略说明:
//			 本策略基于价格与均线建立了一套可变的支撑线阻力线系统, 通过价格与支撑线阻力线的关系进行交易
//			 
// 系统要素:
//			 均线与价格组成的支撑线阻力线系统
//			 1. 当价格死叉均线时, 上根k线的低点为支撑线初始化, 当价格金叉均线时, 上根k线的高点为阻力线初始化
//			 2. 当价格低于均线时不断更新支撑线, 当价格高于均线时不断更新阻力线
//			 3. 当价格金叉均线又死叉均线时记录上根阻力线作为做多的价格线, 当价格死叉均线又金叉均线时记录上根支撑线作为做空的价格线
// 入场条件:
//			 1. 当价格高于做多的价格线时做多
//			 2. 当价格低于做空的价格线时做空
// 出场条件: 
//			 1. 基于ATR的保护性止损
//			 2. 基于ATR的跟踪止损
//
//		 注: 当前策略仅为做多系统, 如需做空, 请参见CL_Moving_Average_Sup_and_Res_S
//----------------------------------------------------------------------//
Params
	Numeric MALength(10);				//均线值
	Numeric ATRLength(10);				//ATR的值
	Numeric ProtectStopATRMulti(0.5);	//保护性止损的ATR乘数
	Numeric TrailStopATRMulti(2.5);		//跟踪止损的ATR乘数
	
Vars
	Series<Numeric> MA(0);				//均线
	Series<Numeric> ATR(0);				//ATR
	
	Series<Numeric> SupportLine(-9999999);		//支撑线
	Series<Numeric> ResistanceLine(9999999);	//阻力线
	
	Series<Bool> CrossFlagForL(False); 	//收盘价与均线交叉的状态记录L
	Series<Bool> CrossFlagForS(False);	//收盘价与均线交叉的状态记录S
	Series<Bool> SupportFlag(False);	//支撑线为真的标志
	Series<Bool> ResistanceFlag(False);	//阻力线为真的标志
	
	Series<Numeric> EntryPriceL(0);		//开仓入场价
	
	Series<Numeric> HighAfterEntry; 	//持仓期间最高点的记录
	Series<Numeric> ProtectStopL;		//基于ATR的保护性止损
	Numeric TrailStopL;					//基于ATR的跟踪止损
	Series<Numeric> MP;					//MarketPosition状态记录
Events
	OnBar(ArrayRef<Integer> indexs)
	{
			
		//系统设置
		//均线与ATR计算
		MA = AverageFC(C,MALength);
		ATR = AvgTrueRange(ATRLength);
		
		//计算入场线
		// 1. 当价格死叉均线时, 上根k线的低点为支撑线初始化, 当价格金叉均线时, 上根k线的高点为阻力线初始化
		// 2. 当价格低于均线时不断更新支撑线, 当价格高于均线时不断更新阻力线
		// 3. 当价格金叉均线又死叉均线时记录上根阻力线作为做多的价格线
		CrossFlagForL = CrossUnder(C,MA);
		CrossFlagForS = CrossOver(C,MA);
		
		If(CrossFlagForL == True)
		{
			If(ResistanceFlag[1] == True)
			{
				EntryPriceL = ResistanceLine[1];
				ResistanceFlag = False;
			}
			SupportFlag = True;
			SupportLine = Low;
		}
		If(CrossFlagForS == True)
		{
			If(SupportFlag[1] == True)
			{
				SupportFlag = False;
			}
			ResistanceFlag = True;
			ResistanceLine = High;
		}
		//更新支撑与阻力线
		If( C > MA )
		{
			If(High > ResistanceLine[1]) ResistanceLine = High;
		}
		Else If(C < MA)
		{
			If(Low < SupportLine[1]) SupportLine = Low;
		}
		
		//系统入场
		//当上根K线的收盘价格金叉多头入场价格线后, 在本根K线开盘价做多
		If(MarketPosition <> 1 And EntryPriceL[1] <> 0 And EntryPriceL[2] <> 0)
		{
			If(Close[2] < EntryPriceL[2] And Close[1] >= EntryPriceL[1] And Vol > 0)
			{
				Buy(0,Open);
				//基于ATR的保护性止损
				ProtectStopL = Low[1] - ProtectStopATRMulti * ATR[1];
			}
		}
		
		//系统出场
		If(BarsSinceEntry == 0)
			HighAfterEntry = High;
		Else	
			HighAfterEntry = Max(HighAfterEntry[1],High);
		//基于ATR的跟踪止损
		TrailStopL = HighAfterEntry[1] - TrailStopATRMulti * ATR[1];
		
		If(MarketPosition == 1 And mp[1] == 1 And Vol > 0)
		{
			//基于ATR的保护性止损
			if(L <= ProtectStopL[1] And ProtectStopL[1] >= TrailStopL)
			{
				Sell(0,Min(Open, ProtectStopL[1]));
			}
			//基于ATR的跟踪止损
			Else if(L<=TrailStopL)
			{
				Sell(0,Min(Open, TrailStopL));
			}
		}
			
			
		MP = MarketPosition;		
	}
//------------------------------------------------------------------------
// 编译版本	GS2014.10.25
// 版权所有	TradeBlazer Software 2003－2025
// 更改声明	TradeBlazer Software保留对TradeBlazer平
//			台每一版本的TradeBlazer公式修改和重写的权利
//------------------------------------------------------------------------