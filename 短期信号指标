秒级别高低点判断指标

  技术指标类：
  - RSI（相对强弱指数）- 超买超卖信号
  - MACD - 快慢线交叉和背离
  - 布林带 - 价格触及上下轨
  - KDJ - K值D值交叉
  - 威廉指标（%R）- 极值反转信号
  - CCI（顺势指标）- 超买超卖区间

  价量指标类：
  - 成交量突增/萎缩
  - 价量背离
  - OBV（能量潮）
  - VWAP偏离度
  - 资金流向指标（MFI）
  - EMV（简易波动指标）
  

  市场微观结构指标：
  - 订单簿失衡度
  - 买卖价差变化
  - 大单流入流出
  - Tick级别价格变化率
  - 高频波动率

  更多经典秒级别技术指标

  趋势类指标：
  - EMA（指数移动平均线）- 多周期EMA金叉死叉
  - PSAR（抛物线转向）- 趋势反转点
  - ADX（平均趋向指数）- 趋势强度判断
  - Aroon（阿隆指标）- 趋势变化预警
  - TRIX（三重指数平滑移动平均）- 趋势转折
  - DPO（区间震荡指标）- 价格周期性波动

  震荡类指标：
  - Stochastic（随机指标）- 快慢随机线交叉
  - ROC（变动率指标）- 价格变化速度
  - PROC（价格震荡指标）- 短期价格动量
  - Ultimate Oscillator（终极震荡指标）- 多时间框架综合
  - TCCI（趋势商品通道指数）- 超买超卖极值

  波动率指标：
  - ATR（真实波动幅度）- 波动率突变
  - Bollinger %B - 布林带位置百分比
  - Keltner通道- 基于ATR的通道突破
  - Donchian通道 - 最高最低价通道
  - Standard Deviation - 标准差波动


  资金流向类指标

  - Chaikin Money Flow (CMF): 基于收盘价位置和成交量的资金流向
  - Accumulation/Distribution Line (A/D): 累积派发线，反映资金累积情况
  - Money Flow Volume: 根据价格变化加权的成交量
  - Positive/Negative Volume Index (PVI/NVI): 区分上涨日和下跌日的成交量指标

  量价动量指标

  - Volume Price Trend (VPT): 量价趋势指标
  - Ease of Movement (EMV): 结合价格变化和成交量的移动便利性
  - Force Index: 价格变化幅度与成交量的乘积
  - Klinger Volume Oscillator: 长短期量价关系的振荡器

  成交量振荡器

  - Chaikin Oscillator: A/D线的MACD形式
  - Volume Oscillator: 短期与长期成交量移动平均的差值
  - Price Volume Oscillator (PVO): 成交量的MACD形式
  - Money Stream: 类似MFI但计算方式略有不同

  综合量价指标

  - Elder's Market Thermometer: 结合价格波动和成交量的市场温度计
  - Twiggs Money Flow: MFI的改进版本

{CHAIKIN MONEY FLOW 背离指标 - CMF DIVERGENCE}
N：9
M：5  {背离判断周期}

{计算收盘位置值 (CLV)}
CLV:=(CLOSE-LOW-HIGH+CLOSE)/(HIGH-LOW);

{计算资金流量}
MF:=CLV*VOL;

{计算CMF}
CMF:=SUM(MF,N)/SUM(VOL,N);

{价格高低点判断}
HH:=HIGH>=HHV(HIGH,M);  {价格创新高}
LL:=LOW<=LLV(LOW,M);    {价格创新低}

{CMF高低点判断}
CMFHH:=CMF>=HHV(CMF,M);  {CMF创新高}
CMFLL:=CMF<=LLV(CMF,M);  {CMF创新低}

{顶背离：价格创新高但CMF未创新高}
顶背离:=HH AND CMFHH=0 AND CMF>0;

{底背离：价格创新低但CMF未创新低}
底背离:=LL AND CMFLL=0 AND CMF<0;

{显示CMF线}
CMF,COLORWHITE,LINETHICK1;
0,LINETHICK1,COLORBLUE;

{背离信号显示}
STICKLINE(顶背离,0,CMF,3,0),COLORRED;
STICKLINE(底背离,0,CMF,3,0),COLORGREEN;

{背离点标记}
DRAWICON(顶背离,CMF,1);  {向下箭头表示顶背离}
DRAWICON(底背离,CMF,2);  {向上箭头表示底背离}

{背离信号文字提示}
DRAWTEXT(顶背离,CMF,'顶背离'),COLORRED;
DRAWTEXT(底背离,CMF,'底背离'),COLORGREEN;
===========================================================

{乖离率背离指标 - BIAS DIVERGENCE}
N:20     {乖离率周期}
M:5      {背离判断周期}

{计算乖离率}
BIAS:=(CLOSE-MA(CLOSE,N))/MA(CLOSE,N)*100;

{价格高低点判断}
PH:=HIGH>=HHV(HIGH,M);  {价格创新高}
PL:=LOW<=LLV(LOW,M);    {价格创新低}

{乖离率高低点判断}
BIASH:=BIAS>=HHV(BIAS,M);  {乖离率创新高}
BIASL:=BIAS<=LLV(BIAS,M);  {乖离率创新低}

{背离判断}
顶背离:=PH AND BIASH=0 AND BIAS>0;
底背离:=PL AND BIASL=0 AND BIAS<0;

{显示乖离率线}
BIAS,COLORWHITE,LINETHICK1;
0,LINETHICK1,COLORBLUE;

{背离信号显示}
STICKLINE(顶背离,0,BIAS,3,0),COLORRED;
STICKLINE(底背离,0,BIAS,3,0),COLORGREEN;

{背离点标记}
DRAWICON(顶背离,BIAS,1);  {顶背离向下箭头}
DRAWICON(底背离,BIAS,2);  {底背离向上箭头}

{背离信号文字提示}
DRAWTEXT(顶背离,BIAS,'顶背离'),COLORRED;
DRAWTEXT(底背离,BIAS,'底背离'),COLORGREEN;

===========================================================
{波动率自适应出场系统 - 主图叠加}
N1:14    {ATR周期}
N2:20    {标准差周期}
N3:10    {成交量波动周期}
基础止损:3   {基础止损百分比}
基础止盈:6   {基础止盈百分比}

{价格波动率评估}
ATR1:=ATR(N1);
ATRMA:=MA(ATR1,N1);
ATR原始评分:=IF(ATRMA>0,(ATR1/ATRMA-1)*100+50,50);
ATR评分:=IF(ATR原始评分>100,100,IF(ATR原始评分<0,0,ATR原始评分));

价格标准差:=STD(CLOSE,N2);
标准差MA:=MA(价格标准差,N2);
标准差原始评分:=IF(标准差MA>0,(价格标准差/标准差MA-1)*100+50,50);
标准差评分:=IF(标准差原始评分>100,100,IF(标准差原始评分<0,0,标准差原始评分));

{成交量波动率评估}
量比:=VOL/MA(VOL,N3);
量比评分:=IF(量比>2,100,IF(量比>1.5,75,IF(量比>1.2,50,IF(量比>0.8,25,0))));

成交量变化率:=ABS(VOL/REF(VOL,1)-1)*100;
量变评分:=IF(成交量变化率>50,100,IF(成交量变化率>30,75,IF(成交量变化率>15,50,25)));

{趋势稳定性评估}
MA5:=MA(CLOSE,5);
MA10:=MA(CLOSE,10);
MA20:=MA(CLOSE,20);
趋势方向:=(MA5>MA10 AND MA10>MA20) OR (MA5<MA10 AND MA10<MA20);
价格偏离度:=ABS(CLOSE/MA20-1)*100;
趋势评分:=IF(趋势方向 AND 价格偏离度<5,20,IF(趋势方向,40,IF(价格偏离度>10,80,60)));

{综合波动率评分}
波动率原始评分:=(ATR评分*0.3+标准差评分*0.2+量比评分*0.2+量变评分*0.15+趋势评分*0.15);
波动率评分:=IF(波动率原始评分>100,100,IF(波动率原始评分<0,0,波动率原始评分));

{自适应系数计算}
自适应系数:=IF(波动率评分<=30,1.5,IF(波动率评分>=70,0.6,1.5-0.9*(波动率评分-30)/40));

{动态止损止盈位计算}
多头止损位:=CLOSE*(1-基础止损*自适应系数/100);
多头止盈位:=CLOSE*(1+基础止盈*自适应系数/100);
空头止损位:=CLOSE*(1+基础止损*自适应系数/100);
空头止盈位:=CLOSE*(1-基础止盈*自适应系数/100);

{出场信号判断}
多头止损信号:=CLOSE<REF(多头止损位,1);
多头止盈信号:=CLOSE>REF(多头止盈位,1);
空头止损信号:=CLOSE>REF(空头止损位,1);
空头止盈信号:=CLOSE<REF(空头止盈位,1);

{主图显示 - 动态止损止盈线}
多头止损位,COLORRED,LINETHICK2;
多头止盈位,COLORGREEN,LINETHICK2;
空头止损位,COLORRED,LINETHICK2;
空头止盈位,COLORGREEN,LINETHICK2;

{波动率状态背景}
STICKLINE(波动率评分<=30,HIGH,LOW,0,1),COLORGREEN;
STICKLINE(波动率评分>=70,HIGH,LOW,0,1),COLORRED;

{出场信号标记}
DRAWICON(多头止损信号,LOW*0.99,1);  {向下箭头}
DRAWICON(多头止盈信号,HIGH*1.01,2); {向上箭头}
DRAWICON(空头止损信号,HIGH*1.01,1); {向下箭头}
DRAWICON(空头止盈信号,LOW*0.99,2);  {向上箭头}

{文字提示}
DRAWTEXT(多头止损信号,LOW*0.98,'多止损'),COLORRED;
DRAWTEXT(多头止盈信号,HIGH*1.02,'多止盈'),COLORGREEN;
DRAWTEXT(空头止损信号,HIGH*1.02,'空止损'),COLORRED;
DRAWTEXT(空头止盈信号,LOW*0.98,'空止盈'),COLORGREEN;

{波动率状态提示}
DRAWTEXT(波动率评分<=30,HIGH*1.03,'低波动'),COLORGREEN;
DRAWTEXT(波动率评分>=70,HIGH*1.03,'高波动'),COLORRED;

===========================================================

{自适应移动止盈线}
N:14     {效率比率计算周期}
止盈系数:1.5  {ATR倍数}

{基础自适应算法}
DIR:=ABS(CLOSE-REF(CLOSE,N)); {整个周期价格的总体变动}
VIR:=SUM(ABS(CLOSE-REF(CLOSE,1)),N); {每个周期价格变动累加}
ER:=DIR/VIR; {价格变动效率}
CS:=ER*(2/3-2/(N+1))+2/(N+1);
CQ:=CS*CS;

{自适应收盘价跟踪}
自适应收盘价:=EMA(DMA(CLOSE,CQ),2);

{ATR动态调整}
当前ATR:=ATR(N);

===========================================================
{第三代智能动态出场模块 - 3RD GENERATION INTELLIGENT EXIT}
基础TR:2.0    {基础止盈止损倍数}
初始止损:1.5  {初始止损ATR倍数}
波动率周期:20  {波动率计算周期}
趋势强度周期:10 {趋势强度评估周期}

{1. 多维度波动率评估模块}
{价格波动率}
当前ATR:=ATR(波动率周期);
ATR均值:=MA(当前ATR,波动率周期*2);
价格波动率:=当前ATR/ATR均值;

{成交量波动率}
量能比:=VOL/MA(VOL,波动率周期);
量能波动率:=STD(量能比,波动率周期)/MA(量能比,波动率周期);

{时间波动率}
价格变化率:=ABS(CLOSE/REF(CLOSE,1)-1);
时间波动率:=MA(价格变化率,5)/MA(价格变化率,波动率周期);

{综合波动率评分}
综合波动率:=(价格波动率*0.5+量能波动率*0.3+时间波动率*0.2);

{2. 市场环境识别模块}
{趋势强度评估}
MA5:=MA(CLOSE,5);
MA10:=MA(CLOSE,10);
MA20:=MA(CLOSE,20);
趋势方向一致:=(MA5>MA10 AND MA10>MA20) OR (MA5<MA10 AND MA10<MA20);
价格偏离度:=ABS(CLOSE/MA20-1);
趋势强度:=IF(趋势方向一致,价格偏离度*100,价格偏离度*50);

{市场状态判断}
强趋势市:=趋势强度>3 AND 趋势方向一致;
震荡市:=趋势强度<1.5;
弱趋势市:=NOT 强趋势市 AND NOT 震荡市;

{3. 智能波动率区域划分}
{动态阈值计算}
历史波动率:=MA(综合波动率,波动率周期*3);
波动率标准差:=STD(综合波动率,波动率周期*2);
下阈值:=历史波动率-波动率标准差*0.5;
上阈值:=历史波动率+波动率标准差*0.5;
极值阈值:=历史波动率+波动率标准差*1.2;

{智能区域划分}
低波动区:=综合波动率<=下阈值;
正常波动区:=综合波动率>下阈值 AND 综合波动率<=上阈值;
高波动区:=综合波动率>上阈值 AND 综合波动率<=极值阈值;
极高波动区:=综合波动率>极值阈值;

{4. 环境自适应TR调整}
{基础TR调整}
TR基础:=IF(低波动区,基础TR*1.8,IF(正常波动区,基础TR,IF(高波动区,基础TR*0.6,基础TR*0.4)));

{趋势环境修正}
TR趋势修正:=IF(强趋势市,TR基础*1.2,IF(震荡市,TR基础*0.8,TR基础));

{最终动态TR}
动态TR:=TR趋势修正;

{2. 移动止盈模块 - 波动率调节}
多头止盈基准:=CLOSE-当前ATR*动态TR;

{只能朝有利方向移动}
多头止盈线:=HHV(多头止盈基准,3);

{3. 反向收敛止损模块}
{假设多头仓位计算}
多头初始止损:=CLOSE-当前ATR*初始止损;
多头当前价格:=CLOSE;

{反向收敛逻辑 - 价格下跌时止损上移}
价格下跌幅度:=MAX(0,REF(CLOSE,1)-CLOSE);
收敛比例:=价格下跌幅度/(当前ATR*初始止损);
多头收敛系数:=MIN(收敛比例,0.8); {最多收敛80%}

多头止损基准:=多头初始止损+当前ATR*初始止损*多头收敛系数;
多头动态止损:=MIN(多头止损基准,CLOSE); {不超过保本价格}


{4. 显示模块}
{移动止盈线显示}
多头止盈线,COLORGREEN,LINETHICK2;

{动态止损线显示}
多头动态止损,COLORBLUE,LINETHICK1,POINTDOT;

===========================================================
{震荡指数 - CHOPPINESS INDEX}
{专门用于识别震荡行情，过滤小幅波动}
N:14     {计算周期}
上限:61.8  {震荡上限阈值}
下限:38.2  {震荡下限阈值}

{计算真实波动范围的对数和}
TR1:=MAX(HIGH-LOW,ABS(HIGH-REF(CLOSE,1)));
TR:=MAX(TR1,ABS(LOW-REF(CLOSE,1)));
ATR_SUM:=SUM(TR,N);

{计算周期内最高价和最低价的差值}
HH:=HHV(HIGH,N);
LL:=LLV(LOW,N);
RANGE:=HH-LL;

{计算震荡指数}
CHOP_RAW:=IF(RANGE>0,LOG(ATR_SUM/RANGE)/LOG(N)*100,50);
CHOP:=IF(CHOP_RAW>100,100,IF(CHOP_RAW<0,0,CHOP_RAW));

{震荡状态判断}
强震荡:=CHOP>上限;
弱震荡:=CHOP<下限;
中性震荡:=CHOP>=下限 AND CHOP<=上限;

{显示震荡指数线}
CHOP,COLORWHITE,LINETHICK2;

{显示阈值线}
上限,COLORRED,LINETHICK1;
下限,COLORGREEN,LINETHICK1;
50,COLORBLUE,LINETHICK1;

{震荡区域背景}
STICKLINE(强震荡,0,100,0,1),COLORRED;
STICKLINE(弱震荡,0,100,0,1),COLORGREEN;

{震荡状态标记}
DRAWTEXT(强震荡 AND REF(强震荡,1)=0,CHOP+5,'震荡开始'),COLORRED;
DRAWTEXT(弱震荡 AND REF(弱震荡,1)=0,CHOP-5,'趋势开始'),COLORGREEN;

{数值显示}
DRAWNUMBER(CHOP>0,CHOP,CHOP),COLORWHITE;

===========================================================
{趋势强度指数 - TREND INTENSITY INDEX}
{结合价格位置、成交量和动量的综合趋势强度指标}
短期:5    {短期均线周期}
中期:10   {中期均线周期}
长期:20   {长期均线周期}
量能周期:14 {成交量评估周期}

{1. 价格位置强度评估}
MA_SHORT:=MA(CLOSE,短期);
MA_MID:=MA(CLOSE,中期);
MA_LONG:=MA(CLOSE,长期);

{均线排列评分}
多头排列:=(MA_SHORT>MA_MID AND MA_MID>MA_LONG);
空头排列:=(MA_SHORT<MA_MID AND MA_MID<MA_LONG);
排列评分:=IF(多头排列,100,IF(空头排列,-100,0));

{价格相对位置}
价格位置:=(CLOSE-MA_LONG)/MA_LONG*100;
位置评分:=IF(ABS(价格位置)>5,IF(价格位置>0,100,-100),价格位置*20);

{2. 动量强度评估}
{价格动量}
ROC5:=(CLOSE/REF(CLOSE,5)-1)*100;
ROC10:=(CLOSE/REF(CLOSE,10)-1)*100;
动量评分:=IF(ABS(ROC5)>3,IF(ROC5>0,100,-100),ROC5*33.33);

{动量一致性}
动量一致:=(ROC5>0 AND ROC10>0) OR (ROC5<0 AND ROC10<0);
一致性评分:=IF(动量一致,50,0);

{3. 成交量确认强度}
{量能比评估}
量能比:=VOL/MA(VOL,量能周期);
量能评分:=IF(量能比>1.5,100,IF(量能比>1.2,75,IF(量能比>1,50,IF(量能比>0.8,25,0))));

{量价配合度}
价格上涨:=CLOSE>REF(CLOSE,1);
价格下跌:=CLOSE<REF(CLOSE,1);
放量上涨:=价格上涨 AND 量能比>1.2;
放量下跌:=价格下跌 AND 量能比>1.2;
缩量横盘:=ABS(CLOSE/REF(CLOSE,1)-1)<0.005 AND 量能比<0.8;

量价配合:=IF(放量上涨,100,IF(放量下跌,-100,IF(缩量横盘,0,50)));

{4. 波动率调整}
{当前波动率}
当前ATR:=ATR(14);
历史ATR:=MA(当前ATR,28);
波动率比:=当前ATR/历史ATR;

{波动率修正系数}
波动率修正:=IF(波动率比>1.5,1.2,IF(波动率比>1.2,1.1,IF(波动率比<0.8,0.8,1)));

{5. 综合趋势强度计算}
{基础强度评分}
基础强度:=(排列评分*0.25+位置评分*0.2+动量评分*0.25+一致性评分*0.1+量能评分*0.1+量价配合*0.1);

{波动率调整后的最终强度}
TII_RAW:=基础强度*波动率修正;
TII:=IF(TII_RAW>100,100,IF(TII_RAW<-100,-100,TII_RAW));

{趋势强度等级划分}
强多头趋势:=TII>60;
弱多头趋势:=TII>20 AND TII<=60;
震荡整理:=TII>=-20 AND TII<=20;
弱空头趋势:=TII>=-60 AND TII<-20;
强空头趋势:=TII<-60;

{显示趋势强度指数}
TII,COLORWHITE,LINETHICK3;

{显示关键水平线}
60,COLORRED,LINETHICK1;
20,COLORGREEN,LINETHICK1;
0,COLORBLUE,LINETHICK2;
-20,COLORGREEN,LINETHICK1;
-60,COLORRED,LINETHICK1;

{趋势强度区域背景}
STICKLINE(强多头趋势,0,TII,0,1),COLORGREEN;
STICKLINE(强空头趋势,0,TII,0,1),COLORRED;
STICKLINE(震荡整理,-100,100,0,1),COLORGRAY;

{趋势变化信号}
多头信号:=TII>20 AND REF(TII,1)<=20;
空头信号:=TII<-20 AND REF(TII,1)>=-20;
震荡信号:=(TII<=20 AND TII>=-20) AND (REF(TII,1)>20 OR REF(TII,1)<-20);

{信号标记}
DRAWICON(多头信号,TII+10,2);  {向上箭头}
DRAWICON(空头信号,TII-10,1);  {向下箭头}
DRAWICON(震荡信号,TII,'○');   {圆圈标记}

{文字提示}
DRAWTEXT(强多头趋势 AND REF(强多头趋势,1)=0,TII+15,'强多头'),COLORGREEN;
DRAWTEXT(强空头趋势 AND REF(强空头趋势,1)=0,TII-15,'强空头'),COLORRED;
DRAWTEXT(震荡信号,TII,'震荡'),COLORYELLOW;

{数值显示}
DRAWNUMBER(TII<>0,TII,TII),COLORWHITE;

===========================================================
{震荡过滤组合应用示例}
{结合CHOP和TII的智能过滤系统}

{震荡过滤条件}
震荡过滤:=CHOP>61.8 AND TII>=-20 AND TII<=20;

{趋势确认条件}
强趋势确认:=CHOP<38.2 AND (TII>60 OR TII<-60);
弱趋势确认:=CHOP<50 AND (TII>20 OR TII<-20);

{交易信号过滤}
允许多头信号:=NOT 震荡过滤 AND TII>0;
允许空头信号:=NOT 震荡过滤 AND TII<0;
暂停交易:=震荡过滤;

{过滤状态显示}
DRAWTEXT(震荡过滤 AND REF(震荡过滤,1)=0,50,'震荡过滤启动'),COLORYELLOW;
DRAWTEXT(强趋势确认 AND REF(强趋势确认,1)=0,80,'强趋势确认'),COLORGREEN;
DRAWTEXT(暂停交易,30,'暂停交易'),COLORRED;

{使用说明}
{
CHOPPINESS INDEX 使用说明：
- CHOP > 61.8: 强震荡，建议暂停交易
- CHOP < 38.2: 趋势性强，可以交易
- CHOP 在38.2-61.8之间: 中性状态

TREND INTENSITY INDEX 使用说明：
- TII > 60: 强多头趋势
- TII 20-60: 弱多头趋势
- TII -20到20: 震荡整理
- TII -60到-20: 弱空头趋势
- TII < -60: 强空头趋势

组合使用建议：
1. 当CHOP>61.8且TII在-20到20之间时，完全暂停交易
2. 当CHOP<38.2且TII>60或TII<-60时，可以积极交易
3. 其他情况谨慎交易，减少仓位
}

