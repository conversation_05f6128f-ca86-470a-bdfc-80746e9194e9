//------------------------------------------------------------------------
// 简称: OscillatorBasedStrategyWithProfitLossRatio_Pure_Long
// 名称: 基于振荡器的纯多头交易策略
// 类别: 交易策略
// 类型: 内建应用
//------------------------------------------------------------------------
// 策略说明:
// 本策略基于振荡器指标进行纯多头交易，通过判断均线差和振荡器比率来决定入场和出场条件。
// 入场条件:
// 1. 当均线差满足条件且振荡器比率满足条件时，开多仓。
// 2. 最小交易量为10股1手。
// 出场条件:
// 1. 当达到预设的盈亏比时，平仓。
// 2. 设置止损条件。
//------------------------------------------------------------------------

Params
	Numeric Fund(20000); // 投入保证金
	Numeric Length(20); // 计算结束点
	Numeric Allow_rev_Signals(1); // 是否允许反转信号，1表示开
	Numeric atr_mul(5); // ATR乘数，用于止损，
	Numeric numberbars(20); // 用于判断回调的周期数
	Numeric i(1); // 达到一级止盈的距离系数参考范围
	Numeric MinTradeSize(10); // 最小交易量，10股1手


Vars
    Series<Numeric> oscVal(0); // 振荡器值
    Series<Numeric> AO(0); // 均线差
    Series<Numeric> mavUp(0); // 均线差大于0的周期数
    Series<Numeric> mavDn(0); // 均线差小于0的周期数
    Series<Numeric> EntryPrice1(0); // 入场价格
    Series<Numeric> Stop_L(0); // 多头止损价格
	Series<Numeric> cond1;
	Series<Numeric> cond2;
	Series<Numeric> cond3;
	Series<Numeric> cond4;
	
	Series<Numeric> bcond1;
	Series<Numeric> bcond2;
	Series<Numeric> bcond3;
	Series<Numeric> bcond4;
	
	global Numeric condd(0);
	Series<Numeric> IsBullishPinbar;
	Array<Numeric> EntryPrices; // 开仓价格数组
    Series<Numeric> SL; // 止损价格数组

    Series<Numeric> ReBars;
	Series<Bool> Time_Close_day;
	Plot plt1; // 第一个子图
    Plot plt2; // 第二个子图
    Plot plt3; // 第三个子图
    Series<Numeric> ATR; // ATR 值
    Series<Numeric> EntryLow; // 入场 Bar 的最低价
    Series<Numeric> EntryHigh; // 入场 Bar 的最低价
	Series<Numeric> entBar;
	Series<Numeric> entPrice ;
    Series<Numeric> Op;
    //Bar线vwap
    Series<Numeric> baramount;
	Series<Numeric> range_dayavg;
	Series<Numeric> barvol;
	Series<Numeric> dayavg;
	Series<Numeric> RD_dayavg;
	
	Series<Numeric> LowestPrice; // 最近最低价
    Series<Numeric> HighestPrice; // 最近最高价
    Series<Numeric> LowestAO; // 最近最低 AO 值
    Series<Numeric> HighestAO; // 最近最高 AO 值
    
    Series<Numeric> BodySize; // 实体大小
    Series<Numeric> UpperShadow; // 上影线大小
    Series<Numeric> LowerShadow; // 下影线大小
    
    Series<Numeric> Jaw; // 鳄鱼线颚线
    Series<Numeric> Teeth; // 鳄鱼线牙齿
    Series<Numeric> Lips; // 鳄鱼线嘴唇
    
    Series<Numeric> HighAfterEntry;//开仓后出现的最高价
	Series<Numeric> LowAfterEntry;//开仓后出现的最低价
	Series<Numeric> barcoutN;
	Series<Numeric> bar_entry_count;
	Series<Numeric> kaicang_kg;
	Series<Numeric> HH;
	Series<Numeric> LL;

	Numeric Start(1); 
	Series<Numeric> HighestLowAfterEntry(0); // 入场后的最高最低价
	
	Series<Numeric> TypicalPrice;
	Series<Numeric> CumulativeTypicalPriceVolume; // 累计典型价格 * 成交量
    Series<Numeric> CumulativeVolume;            // 累计成交量
    Series<Numeric> VWAP;                        // VWAP值
    Series<Bool> InPosition(False);             // 是否处于持仓状态
     
    Series<Numeric> SignalCount;  // 信号出现后的 Bar 计数
    Series<Bool> SignalActive;    // 信号是否有效
	Numeric tp1(0); //跟踪止盈


		
Defs
	Numeric Initialize_Vars_On_Entry()
	{
		entBar = CurrentBar;
		entPrice = EntryPrice;
		Return 1;
	}	
	// 函数：日内开平仓条件
    Numeric IntradayModule()
    {
        // 收盘清仓
        Time_Close_day = (Time>0.1458 and Time <0.1500) or (Time>0.2258 and Time <0.2300); // 收盘前5分钟清仓
        If (Time_Close_day)
        {
            Sell(0, Open); // 平多仓
            Op = 0; // 重置开仓标志
            AO=0;
            CumulativeTypicalPriceVolume=0;
    		CumulativeVolume=0;
    		SignalActive = false; 
    		InPosition=false;
    		condd=0;
        }
        // 当日 Bar 计数
        If (CurrentBar == 0 || (TrueDate(0) != TrueDate(1)) || Time == 0.0900)
        {
            ReBars = 0; // 重置当日 Bar 计数
            Op = 1; // 允许开仓
        }
        Else
        {
            ReBars = ReBars + 1; // 增加当日 Bar 计数
        }
        Return 1;
    }
	//根据atr止损 - 仅多头
	Integer Trailing_Stop(Numeric Enable_Display = 1)
	{
		If(CurrentBar == entBar)
		{
			HighAfterEntry = H ;
			LowAfterEntry  = L ;	
		}Else
		{
			If(MarketPosition > 0)
			{
				HighAfterEntry = MAX(HighAfterEntry, H);				
			}
		}
			Commentary("HighAfterEntry = " + text(HighAfterEntry));
			Commentary("LowAfterEntry  = " + text(LowAfterEntry));
		
		If(MarketPosition >0 && CurrentBar > entBar ) 
		{				
			tp1=LastEntryPrice +  atr_mul*ATR*i; 
			If(HighAfterEntry[1] >= tp1)
			{
				condd=1;	
			}
			if(condd==1)
			{
			Stop_L = max(HighAfterEntry[1]-atr_mul*atr,vwap);
			}
			if(condd==0)
			{
				Stop_L = HighAfterEntry[1]-atr_mul*atr ;	
		}
		}
		
		If(Enable_Display ==1)
		{
			If(MarketPosition >0 And Stop_L <>0 And tp1>0 )
				{
				PlotAuto("Stop_L", Stop_L, Stop_L, Green, Enum_Cross);
				PlotAuto("tp1", tp1, tp1, red, Enum_Cross);
				}
		}
		
		Return 1;
	}
	
	Numeric Reset_Vars_On_Exit()
	{
		entBar = 0;
		entPrice = 0;
		
		Return 1;
	}

Events
    OnInit()
    {
    	Range[0:DataCount-1]
    	{
			//=========数据源相关设置==============
			AddDataFlag(Enum_Data_RolloverBackWard());	//设置后复权

			AddDataFlag(Enum_Data_RolloverRealPrice());	//设置映射真实价格

			AddDataFlag(Enum_Data_AutoSwapPosition());	//设置自动换仓

			//AddDataFlag(Enum_Data_IgnoreSwapSignalCalc());	//设置忽略换仓信号计算
		}
    }

    OnBar(ArrayRef<Integer> indexs)
    {
    	IntradayModule();
    	ATR = Average(TrueRange, 14);
    	//bar线vwap计算
    	TypicalPrice = (High + Low + Close) / 3;
    		if((TrueDate(0)!=TrueDate(1) or time==0.0900)) // 
    		{				
    				baramount=TypicalPrice*Vol;
    				barvol=vol;
    				RD_dayavg=baramount / barvol;				
    		}else
    		{ 
    				baramount = baramount[1]+TypicalPrice*vol;
    				barvol = barvol[1]+vol;
    		}     
            dayavg = baramount / barvol;
            range_dayavg=dayavg-dayavg[1];    		
            if(Time==0.0900 and TIme==0.2100)
            {
    			Return;
            }Else
            {
    			Plotnumeric("dayavg",dayavg);
            } 
		
		// 计算颚线：慢速移动平均线
        Jaw = MinsXAverage(5,(High + Low) / 2, 13);
        // 计算牙齿线：中速移动平均线
        Teeth = MinsXAverage(5,(High + Low) / 2, 8);
        // 计算嘴唇线：快速移动平均线
        Lips = MinsXAverage(5,(High + Low) / 2, 5);
        
        PlotNumeric("Jaw", Jaw,0,Blue); // 颚线
        PlotNumeric("Teeth", Teeth,0, Red); // 牙齿
        PlotNumeric("Lips", Lips,0, Green); // 嘴唇
      
        // 计算均线差
        AO = Average((High + Low) / 2, 5) - Average((High + Low) / 2, 34);

 
        // 计算均线差大于0和小于0的周期数
        mavUp = CountIf(AO > 0, 30);
        mavDn = CountIf(AO < 0, 30);
        // 计算振荡器比率
     
        // 判断是否可以买入或卖出
        Commentary("LowestPrice"+text(LowestPrice[1]));
        Commentary("LowestAO"+text(LowestAO[1]));

		cond1=IIF(mavDn > numberbars,1,0);
		cond2=IIF(AO < 0 ,1,0);
		cond3=IIF(Lips>Teeth && Teeth>=jaw,1,0);
		
        Commentary("cond4[1]"+text(cond4[1]));
        Commentary("Op"+text(Op));
        Commentary("condd"+text(condd));
        Commentary("ReBars"+text(ReBars));
        Commentary("RIsBullishPinbar"+text(IsBullishPinbar));
        HH=Highest(H,Length);
		LL=Lowest(L,Length);
		//日内趋势延续 - 仅多头
        if (MarketPosition==0 &&   cond1[1]==1  &&   cond2[1]==1 &&  cond3[1]==1 && Op==1 && ReBars>0 && AO[1]>AO[2])
        {     
            Buy(MinTradeSize, Open); // 使用最小交易量10股1手
            tp1=LastEntryPrice +  atr_mul*ATR*i;           
			HighestLowAfterEntry = low; // 记录入场后的最高最低价
			Stop_L = HighestLowAfterEntry-atr_mul*atr;
            entBar = CurrentBar; // 记录入场时的 K 线序号
			CumulativeTypicalPriceVolume = TypicalPrice * vol;
            CumulativeVolume = vol;
            InPosition = True;
        }
        
		Commentary("EntryPrice"+text(EntryPrice));
        Commentary("SL"+text(SL));
        Commentary("entBar"+text(entBar));
        Commentary("VWAP"+text(VWAP));
        Commentary("Stop_L"+text(Stop_L));
        LowestPrice = Lowest(Low, length);
		HighestPrice = Highest(High, length);
			// 记录最近最低 AO 值和最高 AO 值
		LowestAO = Lowest(AO, length);
		HighestAO = Highest(AO, length);
		UpperShadow = High - Max(Open, Close);	
		LowerShadow = Min(Open, Close) - Low;
        //日内背离反转策略 - 仅多头
        if (Allow_rev_Signals==1)
        {	
			If(Low < LowestPrice[1] && AO > LowestAO[1] && LowerShadow>0)
        {
            SignalCount = 0;  // 信号出现时重置计数器
            SignalActive = True;  // 信号有效
        }
        // 每经过一根 Bar，计数器加 1
        SignalCount = SignalCount + 1;

        // 如果计数器超过 SignalLength，信号失效
        If(SignalCount > length)
        {
            SignalActive = False;
        }
        	bcond1=IIF(SignalActive[1],1,0);
        	bcond2=IIf(close>Lips,1,0);
			
        	Commentary("bcond1"+text(bcond1));
        	Commentary("bcond2"+text(bcond2));
        	Commentary("SignalCount"+text(SignalCount));
        	if (MarketPosition==0   &&  bcond1[1]==1 &&  bcond2[1]==1 && Op==1  && ReBars>0 && AO[1]>AO[2])
        	{       	
        	Buy(MinTradeSize, Open); // 使用最小交易量10股1手
        	tp1=LastEntryPrice +  atr_mul*ATR*i;
			HighestLowAfterEntry = low; // 记录入场后的最高最低价
			Stop_L = HighestLowAfterEntry-atr_mul*atr;
			entBar = CurrentBar; // 记录入场时的 K 线序号
            CumulativeTypicalPriceVolume = TypicalPrice * vol;
            CumulativeVolume = vol;
            InPosition = True;
        	}
        }
    
        // 如果处于持仓状态，更新累计值
        If (InPosition)
        {
            // 更新累计典型价格 * 成交量
            CumulativeTypicalPriceVolume = CumulativeTypicalPriceVolume[1] + TypicalPrice * vol;

            // 更新累计成交量
            CumulativeVolume = CumulativeVolume[1] + vol;

            // 计算VWAP
            If (CumulativeVolume != 0)
            {
                VWAP = CumulativeTypicalPriceVolume / CumulativeVolume;
            }
            Else
            {
                VWAP = VWAP[1]; // 如果累计成交量为0，保持上一个VWAP值
            }

            // 输出VWAP值
           PlotAuto("VWAP", VWAP, VWAP, Blue, Enum_Cross);
        }
		Trailing_Stop(1);
            If(MarketPosition >0 And BarsSinceEntry >0   ) 
    	{			
    	if (Low <= Stop_L[1] and Stop_L[1]>0)  
    		{
    			Sell(0,Min(Open,Stop_L[1]));
			Stop_L=0;
    			Reset_Vars_On_Exit();
    			InPosition = False;
    			CumulativeTypicalPriceVolume=0;
    			CumulativeVolume=0;	
    			condd=0;
    	}	
    	}
        }
     


//------------------------------------------------------------------------
// 编译版本: GS2014.10.25
// 版权所有: TradeBlazer Software 2003－2025
// 更改声明: TradeBlazer Software保留对TradeBlazer平台每一版本的TradeBlazer公式修改和重写的权利
//------------------------------------------------------------------------
